# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/code/ngspice_parallel

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/code/ngspice_parallel/build

# Utility rule file for run-test.

# Include any custom commands dependencies for this target.
include CMakeFiles/run-test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/run-test.dir/progress.make

CMakeFiles/run-test: ng_shared_parallel_test
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running the NGSpice parallel test program"
	/opt/homebrew/bin/cmake -E echo Running\ NGSpice\ parallel\ test...
	/Users/<USER>/code/ngspice_parallel/build/ng_shared_parallel_test

CMakeFiles/run-test.dir/codegen:
.PHONY : CMakeFiles/run-test.dir/codegen

run-test: CMakeFiles/run-test
run-test: CMakeFiles/run-test.dir/build.make
.PHONY : run-test

# Rule to build all files generated by this target.
CMakeFiles/run-test.dir/build: run-test
.PHONY : CMakeFiles/run-test.dir/build

CMakeFiles/run-test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/run-test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/run-test.dir/clean

CMakeFiles/run-test.dir/depend:
	cd /Users/<USER>/code/ngspice_parallel/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/code/ngspice_parallel /Users/<USER>/code/ngspice_parallel /Users/<USER>/code/ngspice_parallel/build /Users/<USER>/code/ngspice_parallel/build /Users/<USER>/code/ngspice_parallel/build/CMakeFiles/run-test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/run-test.dir/depend

