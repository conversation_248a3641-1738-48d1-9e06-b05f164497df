# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/code/ngspice_parallel

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/code/ngspice_parallel/build

# Utility rule file for prepare-libs.

# Include any custom commands dependencies for this target.
include CMakeFiles/prepare-libs.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/prepare-libs.dir/progress.make

CMakeFiles/prepare-libs:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Copying NGSpice libraries for runtime"
	/opt/homebrew/bin/cmake -E echo Preparing\ shared\ libraries...
	/opt/homebrew/bin/cmake -P /Users/<USER>/code/ngspice_parallel/cmake/PrepareLibs.cmake

CMakeFiles/prepare-libs.dir/codegen:
.PHONY : CMakeFiles/prepare-libs.dir/codegen

prepare-libs: CMakeFiles/prepare-libs
prepare-libs: CMakeFiles/prepare-libs.dir/build.make
.PHONY : prepare-libs

# Rule to build all files generated by this target.
CMakeFiles/prepare-libs.dir/build: prepare-libs
.PHONY : CMakeFiles/prepare-libs.dir/build

CMakeFiles/prepare-libs.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/prepare-libs.dir/cmake_clean.cmake
.PHONY : CMakeFiles/prepare-libs.dir/clean

CMakeFiles/prepare-libs.dir/depend:
	cd /Users/<USER>/code/ngspice_parallel/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/code/ngspice_parallel /Users/<USER>/code/ngspice_parallel /Users/<USER>/code/ngspice_parallel/build /Users/<USER>/code/ngspice_parallel/build /Users/<USER>/code/ngspice_parallel/build/CMakeFiles/prepare-libs.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/prepare-libs.dir/depend

