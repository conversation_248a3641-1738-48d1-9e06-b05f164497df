# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/code/ngspice_parallel

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/code/ngspice_parallel/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/ng_shared_parallel_test.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/ng_shared_parallel_test.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/ng_shared_parallel_test.dir/clean
clean: CMakeFiles/prepare-libs.dir/clean
clean: CMakeFiles/run-test.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/ng_shared_parallel_test.dir

# All Build rule for target.
CMakeFiles/ng_shared_parallel_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ng_shared_parallel_test.dir/build.make CMakeFiles/ng_shared_parallel_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ng_shared_parallel_test.dir/build.make CMakeFiles/ng_shared_parallel_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=1,2 "Built target ng_shared_parallel_test"
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ng_shared_parallel_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/ngspice_parallel/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ng_shared_parallel_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/ngspice_parallel/build/CMakeFiles 0
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/rule

# Convenience name for target.
ng_shared_parallel_test: CMakeFiles/ng_shared_parallel_test.dir/rule
.PHONY : ng_shared_parallel_test

# codegen rule for target.
CMakeFiles/ng_shared_parallel_test.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ng_shared_parallel_test.dir/build.make CMakeFiles/ng_shared_parallel_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=1,2 "Finished codegen for target ng_shared_parallel_test"
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/codegen

# clean rule for target.
CMakeFiles/ng_shared_parallel_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ng_shared_parallel_test.dir/build.make CMakeFiles/ng_shared_parallel_test.dir/clean
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/prepare-libs.dir

# All Build rule for target.
CMakeFiles/prepare-libs.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/prepare-libs.dir/build.make CMakeFiles/prepare-libs.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/prepare-libs.dir/build.make CMakeFiles/prepare-libs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=3 "Built target prepare-libs"
.PHONY : CMakeFiles/prepare-libs.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/prepare-libs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/ngspice_parallel/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/prepare-libs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/ngspice_parallel/build/CMakeFiles 0
.PHONY : CMakeFiles/prepare-libs.dir/rule

# Convenience name for target.
prepare-libs: CMakeFiles/prepare-libs.dir/rule
.PHONY : prepare-libs

# codegen rule for target.
CMakeFiles/prepare-libs.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/prepare-libs.dir/build.make CMakeFiles/prepare-libs.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=3 "Finished codegen for target prepare-libs"
.PHONY : CMakeFiles/prepare-libs.dir/codegen

# clean rule for target.
CMakeFiles/prepare-libs.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/prepare-libs.dir/build.make CMakeFiles/prepare-libs.dir/clean
.PHONY : CMakeFiles/prepare-libs.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/run-test.dir

# All Build rule for target.
CMakeFiles/run-test.dir/all: CMakeFiles/ng_shared_parallel_test.dir/all
CMakeFiles/run-test.dir/all: CMakeFiles/prepare-libs.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run-test.dir/build.make CMakeFiles/run-test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run-test.dir/build.make CMakeFiles/run-test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=4 "Built target run-test"
.PHONY : CMakeFiles/run-test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run-test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/ngspice_parallel/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/run-test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/ngspice_parallel/build/CMakeFiles 0
.PHONY : CMakeFiles/run-test.dir/rule

# Convenience name for target.
run-test: CMakeFiles/run-test.dir/rule
.PHONY : run-test

# codegen rule for target.
CMakeFiles/run-test.dir/codegen: CMakeFiles/ng_shared_parallel_test.dir/all
CMakeFiles/run-test.dir/codegen: CMakeFiles/prepare-libs.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run-test.dir/build.make CMakeFiles/run-test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=4 "Finished codegen for target run-test"
.PHONY : CMakeFiles/run-test.dir/codegen

# clean rule for target.
CMakeFiles/run-test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run-test.dir/build.make CMakeFiles/run-test.dir/clean
.PHONY : CMakeFiles/run-test.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

