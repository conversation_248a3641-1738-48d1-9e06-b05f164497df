# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/code/ngspice_parallel

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/code/ngspice_parallel/build

# Include any dependencies generated for this target.
include CMakeFiles/ng_shared_parallel_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ng_shared_parallel_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ng_shared_parallel_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ng_shared_parallel_test.dir/flags.make

CMakeFiles/ng_shared_parallel_test.dir/codegen:
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/codegen

CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o: CMakeFiles/ng_shared_parallel_test.dir/flags.make
CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o: /Users/<USER>/code/ngspice_parallel/ng_shared_parallel/main.c
CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o: CMakeFiles/ng_shared_parallel_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o -MF CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o.d -o CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o -c /Users/<USER>/code/ngspice_parallel/ng_shared_parallel/main.c

CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/code/ngspice_parallel/ng_shared_parallel/main.c > CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.i

CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/code/ngspice_parallel/ng_shared_parallel/main.c -o CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.s

# Object files for target ng_shared_parallel_test
ng_shared_parallel_test_OBJECTS = \
"CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o"

# External object files for target ng_shared_parallel_test
ng_shared_parallel_test_EXTERNAL_OBJECTS =

ng_shared_parallel_test: CMakeFiles/ng_shared_parallel_test.dir/ng_shared_parallel/main.c.o
ng_shared_parallel_test: CMakeFiles/ng_shared_parallel_test.dir/build.make
ng_shared_parallel_test: /opt/local/lib/libngspice.dylib
ng_shared_parallel_test: CMakeFiles/ng_shared_parallel_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/code/ngspice_parallel/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable ng_shared_parallel_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ng_shared_parallel_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ng_shared_parallel_test.dir/build: ng_shared_parallel_test
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/build

CMakeFiles/ng_shared_parallel_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ng_shared_parallel_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/clean

CMakeFiles/ng_shared_parallel_test.dir/depend:
	cd /Users/<USER>/code/ngspice_parallel/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/code/ngspice_parallel /Users/<USER>/code/ngspice_parallel /Users/<USER>/code/ngspice_parallel/build /Users/<USER>/code/ngspice_parallel/build /Users/<USER>/code/ngspice_parallel/build/CMakeFiles/ng_shared_parallel_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ng_shared_parallel_test.dir/depend

