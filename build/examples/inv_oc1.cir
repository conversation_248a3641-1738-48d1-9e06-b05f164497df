*****************==== CMOS Inverter chain, first partition ====*******************

.include modelcard.nmos
.include modelcard.pmos

*.MODEL  N1  NMOS  LEVEL = 14  VERSION = 4.7
*.MODEL  P1  PMOS  LEVEL = 14  VERSION = 4.7

vdd 1 0 1.8
*vin in1 0 dc 0 external
vin in1 0 dc 0 pulse(0 1.8 0 0.2n 0.2n 1.2n 2.8n)
vc1 c1 0 dc 1.8 pulse(1.8 0 45n 0.2n 0.2n 10n 200n)

mp1 3 in1 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn1 3 in1 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
mp2 4 3 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn2 4 3 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u 
xnand 4 c1 44 1 NAND
mp3 5 44 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn3 5 44 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u  
mp4 6 5 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn4 6 5 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
mp5 7 6 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn5 7 6 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
mp6 8 7 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn6 8 7 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
mp7 9 8 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn7 9 8 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
mp8 out1 9 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn8 out1 9 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
*buffer 
mp9 buf out1 1 1 p1 l=0.1u w=10u ad=5p pd=11u as=5p ps=11u 
mn9 buf out1 0 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u

.SUBCKT NAND in1 in2 out Vdd
*   NODES:  INPUT(2), OUTPUT, VCC
M1 out in2 Vdd Vdd p1 l=0.1u  w=10u ad=5p pd=11u as=5p ps=11u 
M2 net.1 in2 0 0 n1   l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
M3 out in1 Vdd Vdd p1 l=0.1u  w=10u ad=5p pd=11u as=5p ps=11u 
M4 out in1 net.1 0 n1 l=0.1u w=5u ad=2.5p pd=6u as=2.5p ps=6u
.ENDS NAND
 
*.ic v(2)=1.0 v(3)=1.0 v(4)=1.0
*.ic v(2)=2.0 v(3)=0.0 v(4)=2.0
*.ic v(2)=2.0 v(3)=0.0 v(4)=2.0 v(5)=0.0 v(6)=2.0 
*.ic v(7)=0.0 v(8)=2.0 v(9)=0.0 v(10)=2.0 v(11)=0.0 v(12)=2.0
*.ic v(13)=0.0 v(14)=2.0 v(15)=0.0 v(16)=2.0 v(17)=0.0 v(18)=2.0
*.ic V(10)=5 v(2)=5 v(3)=5 v(4)=5 v(5)=5 v(6)=5


.tran .1ns 0.2us
.save V(out1) V(buf)

.end
