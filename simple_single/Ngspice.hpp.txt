/**********************************************************************
 *
 * FaradayEDA 
 * http://www.faradynamics.com
 *
 * Copyright (C) 2017-2019 Faraday Dynamics, Ltd.
 * All rights reserved.
 *
 * Author: <EMAIL>
 *
 * Version V201904
 * Date: 2019-03-07 11:16:12
 *
 **********************************************************************/

#ifndef FARADY_NGSPICE_HPP
#define FARADY_NGSPICE_HPP

#include <complex>
#include <memory>
#include <string>
#include <vector>
#include <map>

namespace Farady {

class Ngspice {
  public:
    static std::shared_ptr<Ngspice> instance();
    std::shared_ptr<Ngspice> instance1();
    // Execute ngspice command
    bool command(const std::string& cmd) const;
    void init() const;
    // Load netlist into ngspice
    // Netlist is divided into lines
    int load_netlist(const std::vector<std::string>&) const;
    bool run() const;
    std::vector<std::complex<double>> ivec(const std::string& label) const;
    std::vector<std::complex<double>> vvec(const std::string &label) const;
    std::vector<double> ivec_dc(const std::string &label, const std::string &type = "current") const;
    std::map<std::string, double> ivec_dc_current(const std::string &device_name) const;
    std::vector<double> tran_vvec(const std::string &label) const;
    std::vector<double> tran_ivec(const std::string &label) const;
    Ngspice();

  private:
    // Callback functions
    static int cbSendChar(char*, int, void*);
    static int cbSendStat(char*, int, void*);
    static int cbControlledExit(int, bool, bool, int, void*);
    static int cbBGThreadRunning(bool, int, void*);
};

} // namespace Farady

#endif