# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ngspice_parallel/simple_single

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ngspice_parallel/simple_single/build

# Include any dependencies generated for this target.
include CMakeFiles/ngspice_simple_single.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ngspice_simple_single.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ngspice_simple_single.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ngspice_simple_single.dir/flags.make

CMakeFiles/ngspice_simple_single.dir/main.c.o: CMakeFiles/ngspice_simple_single.dir/flags.make
CMakeFiles/ngspice_simple_single.dir/main.c.o: ../main.c
CMakeFiles/ngspice_simple_single.dir/main.c.o: CMakeFiles/ngspice_simple_single.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngspice_parallel/simple_single/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/ngspice_simple_single.dir/main.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/ngspice_simple_single.dir/main.c.o -MF CMakeFiles/ngspice_simple_single.dir/main.c.o.d -o CMakeFiles/ngspice_simple_single.dir/main.c.o -c /home/<USER>/ngspice_parallel/simple_single/main.c

CMakeFiles/ngspice_simple_single.dir/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/ngspice_simple_single.dir/main.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngspice_parallel/simple_single/main.c > CMakeFiles/ngspice_simple_single.dir/main.c.i

CMakeFiles/ngspice_simple_single.dir/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/ngspice_simple_single.dir/main.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngspice_parallel/simple_single/main.c -o CMakeFiles/ngspice_simple_single.dir/main.c.s

# Object files for target ngspice_simple_single
ngspice_simple_single_OBJECTS = \
"CMakeFiles/ngspice_simple_single.dir/main.c.o"

# External object files for target ngspice_simple_single
ngspice_simple_single_EXTERNAL_OBJECTS =

ngspice_simple_single: CMakeFiles/ngspice_simple_single.dir/main.c.o
ngspice_simple_single: CMakeFiles/ngspice_simple_single.dir/build.make
ngspice_simple_single: /usr/local/lib/libngspice.so
ngspice_simple_single: CMakeFiles/ngspice_simple_single.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/ngspice_parallel/simple_single/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable ngspice_simple_single"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ngspice_simple_single.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ngspice_simple_single.dir/build: ngspice_simple_single
.PHONY : CMakeFiles/ngspice_simple_single.dir/build

CMakeFiles/ngspice_simple_single.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ngspice_simple_single.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ngspice_simple_single.dir/clean

CMakeFiles/ngspice_simple_single.dir/depend:
	cd /home/<USER>/ngspice_parallel/simple_single/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ngspice_parallel/simple_single /home/<USER>/ngspice_parallel/simple_single /home/<USER>/ngspice_parallel/simple_single/build /home/<USER>/ngspice_parallel/simple_single/build /home/<USER>/ngspice_parallel/simple_single/build/CMakeFiles/ngspice_simple_single.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/ngspice_simple_single.dir/depend

