#include "Ngspice.hpp"
#include <complex>
#include <cstddef>
#include <cstring>
#include <cstdlib>
#include <iostream>
#include <memory>
#include <ngspice/sharedspice.h>
#include <string>
#include <map>
#include <system_error>
#include <vector>
using std::cout;
using std::endl;
using std::shared_ptr;
using std::string;
using std::to_string;
using std::unique_ptr;
using std::vector;
using std::map;
using Complex = std::complex<double>;
#define NDEBUG
namespace Farady
{
shared_ptr<Ngspice> Ngspice::instance()
{
    static shared_ptr<Ngspice> instance;
	// instance.reset();
	// instance = NULL;
	if (!instance) {
		instance.reset(new Ngspice());
	}
	
	return instance;
}
// top -d 1 | grep wanggang | grep python
int Ngspice::cbSendChar(char *what, int id, void *user)
{
#ifdef NDEBUG
	// cout << "ngspice: " << what << id << endl;
#endif
	return 0;
}

int Ngspice::cbSendStat(char *what, int id, void *user)
{
#ifdef NDEBUG
	// cout << "ngspice: " << what<< id << endl;
#endif
	return 0;
}

int Ngspice::cbControlledExit(int status, bool immediate, bool exit_upon_quit, int id, void *user)
{
#ifdef NDEBUG
	cout << "exit status: " << status << endl;
#endif
	return 0;
}

int Ngspice::cbBGThreadRunning(bool is_running, int id, void *user)
{
	if (is_running)
		printf("lib %d: bg not running\n", id);
	else
		printf("lib %d: bg running\n", id);
	return 0;
}

Ngspice::Ngspice()
{
	ngSpice_Init(&cbSendChar, &cbSendStat, &cbControlledExit, NULL, NULL, &cbBGThreadRunning, NULL);
}

bool Ngspice::command(const string &cmd) const
{
	ngSpice_Command(const_cast<char *>(cmd.c_str()));
	return true;
}

void Ngspice::init() const
{
	command("reset");
	command("destroy all");
	command("remcirc");
}

int Ngspice::load_netlist(const vector<string> &nl) const
{
	auto circ_array = std::make_unique<char*[]>(nl.size() + 1);
    for (size_t i = 0; i < nl.size(); ++i) {
        circ_array[i] = strdup(nl[i].c_str());
        if (!circ_array[i]) {
            throw std::bad_alloc();
		}
	}
	circ_array[nl.size()] = nullptr;
	int result = ngSpice_Circ(circ_array.get());
	return result;
}

bool Ngspice::run() const
{
	bool isok = command("bg_run");
	return isok;
}

vector<Complex> Ngspice::ivec(const string &label) const
{
	vector<Complex> data;
	string name = string("i(") + label + string(")");
	pvector_info vi = ngGet_Vec_Info(const_cast<char *>(name.c_str()));
	if (vi) {
		for (auto t = 0; t < vi->v_length; ++t) {
			data.push_back(Complex(vi->v_compdata[t].cx_real, vi->v_compdata[t].cx_imag));
		}
	} else {
	}
	return data;
}

vector<Complex> Ngspice::vvec(const string &label) const
{
	vector<Complex> data;
	string name = string("v(") + label + string(")");
	pvector_info vi = ngGet_Vec_Info(const_cast<char *>(name.c_str()));
	if (vi) {
		for (auto t = 0; t < vi->v_length; ++t) {
			data.push_back(Complex(vi->v_compdata[t].cx_real, vi->v_compdata[t].cx_imag));
		}
	}
	return data;
}

vector<double> Ngspice::tran_vvec(const string &label) const
{
	vector<double> data;
	string name = string("v(") + label + string(")");
	pvector_info vi = ngGet_Vec_Info(const_cast<char *>(name.c_str()));
	if (vi) {
		for (auto t = 0; t < vi->v_length; ++t) {
			data.push_back(vi->v_realdata[t]);
//			data.push_back(vi->v_compdata[t].cx_imag);		
// #ifdef NDEBUG
// 	cout<<vi->v_realdata[t]<<endl;
// #endif
		}
	} else {
	}
	return data;
}
vector<double> Ngspice::tran_ivec(const string &label) const
{
	vector<double> data;
	string name = string("i(") + label + string(")");
	pvector_info vi = ngGet_Vec_Info(const_cast<char *>(name.c_str()));
	if (vi) {
		for (auto t = 0; t < vi->v_length; ++t) {
			data.push_back(vi->v_realdata[t]);
		}
	} else {
	}
	return data;
}

vector<double> Ngspice::ivec_dc(const string &label, const string &type) const
{
	vector<double> data;
	string name;
	if(type =="current"){
		name = string("i(") + label + string(")");
	}else if(type =="voltage"){
		name = string("v(") + label + string(")"); 
	}
	pvector_info vi = ngGet_Vec_Info(const_cast<char *>(name.c_str()));
	if (vi) {
		for (auto t = 0; t < vi->v_length; ++t) {
			data.push_back(vi->v_realdata[t]);
		}
	} else {
	}
	return data;
}
map<string, double> Ngspice::ivec_dc_current(const string &device_name) const
{
	double current;
	map<string, double> all_current;
	vector<string> name;
	if(device_name.at(0) == 'R' || device_name.at(0) == 'r' ||
	  device_name.at(0) == 'L' || device_name.at(0) == 'l' ||
	  device_name.at(0) == 'C' || device_name.at(0) == 'c' ){
		name.push_back("@" + device_name + "[i]");
	}
	if(device_name.at(0) == 'D' || device_name.at(0) == 'd' ){
		name.push_back("@" + device_name + "[id]");
	}
	if(device_name.at(0) == 'M' || device_name.at(0) == 'm' ){
		name.push_back("@" + device_name + "[ib]");
		name.push_back("@" + device_name + "[id]");
		name.push_back("@" + device_name + "[ig]");
		name.push_back("@" + device_name + "[is]");
	}
	if(device_name.at(0) == 'Q' || device_name.at(0) == 'q' ){
		name.push_back("@" + device_name + "[ib]");
		name.push_back("@" + device_name + "[ic]");
		name.push_back("@" + device_name + "[ie]");
		name.push_back("@" + device_name + "[is]");
	}
	if(device_name.at(0) == 'J' || device_name.at(0) == 'j' ){
		name.push_back("@" + device_name + "[id]");
		name.push_back("@" + device_name + "[ig]");
		name.push_back("@" + device_name + "[igd]");
		name.push_back("@" + device_name + "[is]");
		
	}
	if(device_name.at(0) == 'V' || device_name.at(0) == 'v' ){
		name.push_back("i(" + device_name + ")");
	}
	for(int i=0; i< name.size(); i++){
		pvector_info vi = ngGet_Vec_Info(const_cast<char *>(name.at(i).c_str()));
		if (vi) {
			for (auto t = 0; t < vi->v_length; ++t) {
				current = vi->v_realdata[t];
				all_current.insert(make_pair(name.at(i), current));
			}
		} else {
		}
	}
	return all_current;
}

} // namespace Farady